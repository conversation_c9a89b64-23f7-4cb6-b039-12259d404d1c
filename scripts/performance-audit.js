#!/usr/bin/env node

/**
 * Performance Audit Script for Ottawa Shine Solutions
 * Analyzes build output and provides performance recommendations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DIST_DIR = path.join(__dirname, '..', 'dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

// Performance thresholds (in bytes)
const THRESHOLDS = {
  JS_CHUNK_SIZE: 250 * 1024, // 250KB
  CSS_FILE_SIZE: 50 * 1024,  // 50KB
  IMAGE_SIZE: 100 * 1024,    // 100KB
  TOTAL_JS_SIZE: 500 * 1024, // 500KB
  TOTAL_CSS_SIZE: 100 * 1024 // 100KB
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function analyzeFiles(directory) {
  const files = fs.readdirSync(directory);
  const analysis = {
    js: [],
    css: [],
    images: [],
    other: []
  };

  files.forEach(file => {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      const size = stats.size;
      const ext = path.extname(file).toLowerCase();
      
      const fileInfo = {
        name: file,
        size: size,
        formattedSize: formatBytes(size)
      };

      if (ext === '.js') {
        analysis.js.push(fileInfo);
      } else if (ext === '.css') {
        analysis.css.push(fileInfo);
      } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
        analysis.images.push(fileInfo);
      } else {
        analysis.other.push(fileInfo);
      }
    }
  });

  // Sort by size (largest first)
  Object.keys(analysis).forEach(key => {
    analysis[key].sort((a, b) => b.size - a.size);
  });

  return analysis;
}

function generateReport(analysis) {
  console.log('\n🚀 Ottawa Shine Solutions - Performance Audit Report');
  console.log('=' .repeat(60));

  // JavaScript Analysis
  console.log('\n📦 JavaScript Files:');
  const totalJSSize = analysis.js.reduce((sum, file) => sum + file.size, 0);
  console.log(`Total JS Size: ${formatBytes(totalJSSize)}`);
  
  if (totalJSSize > THRESHOLDS.TOTAL_JS_SIZE) {
    console.log('⚠️  WARNING: Total JS size exceeds recommended threshold');
  } else {
    console.log('✅ Total JS size is within recommended limits');
  }

  analysis.js.forEach(file => {
    const status = file.size > THRESHOLDS.JS_CHUNK_SIZE ? '⚠️ ' : '✅';
    console.log(`  ${status} ${file.name}: ${file.formattedSize}`);
  });

  // CSS Analysis
  console.log('\n🎨 CSS Files:');
  const totalCSSSize = analysis.css.reduce((sum, file) => sum + file.size, 0);
  console.log(`Total CSS Size: ${formatBytes(totalCSSSize)}`);
  
  if (totalCSSSize > THRESHOLDS.TOTAL_CSS_SIZE) {
    console.log('⚠️  WARNING: Total CSS size exceeds recommended threshold');
  } else {
    console.log('✅ Total CSS size is within recommended limits');
  }

  analysis.css.forEach(file => {
    const status = file.size > THRESHOLDS.CSS_FILE_SIZE ? '⚠️ ' : '✅';
    console.log(`  ${status} ${file.name}: ${file.formattedSize}`);
  });

  // Images Analysis
  console.log('\n🖼️  Image Files:');
  const totalImageSize = analysis.images.reduce((sum, file) => sum + file.size, 0);
  console.log(`Total Image Size: ${formatBytes(totalImageSize)}`);

  analysis.images.forEach(file => {
    const status = file.size > THRESHOLDS.IMAGE_SIZE ? '⚠️ ' : '✅';
    console.log(`  ${status} ${file.name}: ${file.formattedSize}`);
  });

  // Overall Summary
  const totalSize = totalJSSize + totalCSSSize + totalImageSize;
  console.log('\n📊 Summary:');
  console.log(`Total Bundle Size: ${formatBytes(totalSize)}`);
  console.log(`JavaScript: ${formatBytes(totalJSSize)} (${((totalJSSize / totalSize) * 100).toFixed(1)}%)`);
  console.log(`CSS: ${formatBytes(totalCSSSize)} (${((totalCSSSize / totalSize) * 100).toFixed(1)}%)`);
  console.log(`Images: ${formatBytes(totalImageSize)} (${((totalImageSize / totalSize) * 100).toFixed(1)}%)`);

  // Recommendations
  console.log('\n💡 Recommendations:');
  
  if (totalJSSize > THRESHOLDS.TOTAL_JS_SIZE) {
    console.log('• Consider further code splitting to reduce JavaScript bundle size');
    console.log('• Review and remove unused dependencies');
    console.log('• Implement dynamic imports for non-critical features');
  }

  if (totalCSSSize > THRESHOLDS.TOTAL_CSS_SIZE) {
    console.log('• Consider purging unused CSS classes');
    console.log('• Split CSS by routes if possible');
  }

  const largeImages = analysis.images.filter(img => img.size > THRESHOLDS.IMAGE_SIZE);
  if (largeImages.length > 0) {
    console.log('• Optimize large images:');
    largeImages.forEach(img => {
      console.log(`  - ${img.name} (${img.formattedSize})`);
    });
    console.log('• Consider using WebP format for better compression');
    console.log('• Implement responsive images with different sizes');
  }

  // Performance Score
  let score = 100;
  if (totalJSSize > THRESHOLDS.TOTAL_JS_SIZE) score -= 20;
  if (totalCSSSize > THRESHOLDS.TOTAL_CSS_SIZE) score -= 10;
  if (largeImages.length > 0) score -= (largeImages.length * 5);

  console.log(`\n🎯 Performance Score: ${Math.max(0, score)}/100`);
  
  if (score >= 90) {
    console.log('🎉 Excellent! Your bundle is well optimized.');
  } else if (score >= 70) {
    console.log('👍 Good optimization, but there\'s room for improvement.');
  } else {
    console.log('⚠️  Bundle needs optimization to improve performance.');
  }

  console.log('\n' + '='.repeat(60));
}

function main() {
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Build directory not found. Please run "npm run build" first.');
    process.exit(1);
  }

  if (!fs.existsSync(ASSETS_DIR)) {
    console.error('❌ Assets directory not found in build output.');
    process.exit(1);
  }

  const analysis = analyzeFiles(ASSETS_DIR);
  generateReport(analysis);

  // Also check for other important files
  const indexHtml = path.join(DIST_DIR, 'index.html');
  if (fs.existsSync(indexHtml)) {
    const htmlSize = getFileSize(indexHtml);
    console.log(`\n📄 index.html: ${formatBytes(htmlSize)}`);
  }

  console.log('\n✨ Audit complete! Use these insights to optimize your application.');
}

main();
