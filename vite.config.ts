import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Custom plugin to remove console logs in production
    {
      name: 'remove-console',
      transform(code, _id) {
        if (process.env.NODE_ENV === 'production') {
          return {
            code: code.replace(/console\.(log|debug|info|warn|error|assert|dir|dirxml|trace|group|groupEnd|time|timeEnd|profile|profileEnd|count)\(.*?\);?/g, ''),
            map: null
          }
        }
      }
    }
  ],
  build: {
    // Enable minification and optimization (using esbuild which is faster)
    minify: 'esbuild',
    // Enable code splitting for better lazy loading
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // More granular chunking strategy
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            if (id.includes('firebase')) {
              return 'firebase-vendor';
            }
            if (id.includes('lucide-react')) {
              return 'icons-vendor';
            }
            if (id.includes('react-router')) {
              return 'router-vendor';
            }
            // Group other vendor libraries
            return 'vendor';
          }
          // Split pages into separate chunks
          if (id.includes('src/pages/')) {
            const pageName = id.split('/pages/')[1].split('.')[0].toLowerCase();
            return `page-${pageName}`;
          }
        }
      }
    },
    // Improve chunk size warnings threshold
    chunkSizeWarningLimit: 500,
    // Enable source maps for production debugging (optional)
    sourcemap: false,
    // Optimize CSS
    cssMinify: true
  },
  // Optimize deps for faster dev server
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'lucide-react',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'react-router-dom'
    ],
    exclude: ['firebase']
  },
  // Configure server for development
  server: {
    headers: {
      // Cache static assets for 1 year
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  },
  // Configure preview server
  preview: {
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  }
})
