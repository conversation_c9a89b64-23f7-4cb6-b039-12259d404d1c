import { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { lazy } from 'react';
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';
import AnalyticsProvider from './components/AnalyticsProvider';
import PerformanceMonitor from './components/PerformanceMonitor';
import { AuthProvider } from './contexts/AuthContext';

// Lazy load page components
const HomePage = lazy(() => import('./pages/HomePage'));
const ServicesPage = lazy(() => import('./pages/ServicesPage'));
const QuotePage = lazy(() => import('./pages/QuotePage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));
const AdminPage = lazy(() => import('./pages/AdminPage'));
const LoginPage = lazy(() => import('./pages/LoginPage'));

function App() {
  return (
    <AuthProvider>
      <Router>
        <AnalyticsProvider>
          <div className="min-h-screen bg-gray-50">
            <ErrorBoundary>
              <Suspense fallback={<LoadingSpinner size="large" text="Loading..." />}>
              <Routes>
                {/* Public routes with navigation and footer */}
                <Route
                  path="/"
                  element={
                    <>
                      <Navigation />
                      <HomePage />
                      <Footer />
                    </>
                  }
                />
                <Route
                  path="/services"
                  element={
                    <>
                      <Navigation />
                      <ServicesPage />
                      <Footer />
                    </>
                  }
                />
                <Route
                  path="/quote"
                  element={
                    <>
                      <Navigation />
                      <QuotePage />
                      <Footer />
                    </>
                  }
                />
                <Route
                  path="/contact"
                  element={
                    <>
                      <Navigation />
                      <ContactPage />
                      <Footer />
                    </>
                  }
                />
                
                {/* Auth routes - minimal layout */}
                <Route path="/login" element={<LoginPage />} />
                
                {/* Protected admin route */}
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute>
                      <AdminPage />
                    </ProtectedRoute>
                  }
                />
                
                {/* Catch all - redirect to home */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </ErrorBoundary>

          {/* Performance Monitor (dev only) */}
          <PerformanceMonitor />
        </div>
        </AnalyticsProvider>
      </Router>
    </AuthProvider>
  );
}

export default App;
