import React, { useEffect, useState } from 'react';
import { Activity, Clock, Zap, Globe } from 'lucide-react';

interface PerformanceMetrics {
  fcp: number;
  lcp: number;
  fid: number;
  cls: number;
  ttfb: number;
  domContentLoaded: number;
  loadComplete: number;
}

interface NetworkInfo {
  effectiveType: string;
  downlink: number;
  rtt: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [networkInfo, setNetworkInfo] = useState<NetworkInfo | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (import.meta.env.DEV) {
      // Collect performance metrics
      const collectMetrics = () => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const newMetrics: PerformanceMetrics = {
            fcp: 0,
            lcp: 0,
            fid: 0,
            cls: 0,
            ttfb: navigation.responseStart - navigation.requestStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart
          };

          // Observe Core Web Vitals
          if ('PerformanceObserver' in window) {
            // LCP
            new PerformanceObserver((entryList) => {
              const entries = entryList.getEntries();
              const lastEntry = entries[entries.length - 1];
              newMetrics.lcp = lastEntry.startTime;
              setMetrics({ ...newMetrics });
            }).observe({ entryTypes: ['largest-contentful-paint'] });

            // FID
            new PerformanceObserver((entryList) => {
              for (const entry of entryList.getEntries()) {
                const fidEntry = entry as any; // PerformanceEventTiming
                newMetrics.fid = fidEntry.processingStart - fidEntry.startTime;
                setMetrics({ ...newMetrics });
              }
            }).observe({ entryTypes: ['first-input'] });

            // CLS
            let clsValue = 0;
            new PerformanceObserver((entryList) => {
              for (const entry of entryList.getEntries()) {
                const clsEntry = entry as any; // LayoutShift
                if (!clsEntry.hadRecentInput) {
                  clsValue += clsEntry.value;
                }
              }
              newMetrics.cls = clsValue;
              setMetrics({ ...newMetrics });
            }).observe({ entryTypes: ['layout-shift'] });

            // FCP
            new PerformanceObserver((entryList) => {
              const entries = entryList.getEntries();
              const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
              if (fcpEntry) {
                newMetrics.fcp = fcpEntry.startTime;
                setMetrics({ ...newMetrics });
              }
            }).observe({ entryTypes: ['paint'] });
          }

          setMetrics(newMetrics);
        }

        // Get network information
        const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
        if (connection) {
          setNetworkInfo({
            effectiveType: connection.effectiveType || 'unknown',
            downlink: connection.downlink || 0,
            rtt: connection.rtt || 0
          });
        }
      };

      // Collect metrics after page load
      if (document.readyState === 'complete') {
        setTimeout(collectMetrics, 1000);
      } else {
        window.addEventListener('load', () => {
          setTimeout(collectMetrics, 1000);
        });
      }

      // Show monitor after 3 seconds
      setTimeout(() => setIsVisible(true), 3000);
    }
  }, []);

  const getScoreColor = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.poor) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (!import.meta.env.DEV || !isVisible || !metrics) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <Activity className="h-4 w-4 text-blue-600 mr-2" />
          <h3 className="text-sm font-semibold text-gray-900">Performance</h3>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600"
          aria-label="Close performance monitor"
        >
          ×
        </button>
      </div>

      <div className="space-y-2 text-xs">
        {/* Core Web Vitals */}
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1 text-gray-500" />
            <span className="text-gray-600">FCP:</span>
            <span className={`ml-1 font-mono ${getScoreColor(metrics.fcp, { good: 1800, poor: 3000 })}`}>
              {formatTime(metrics.fcp)}
            </span>
          </div>
          
          <div className="flex items-center">
            <Zap className="h-3 w-3 mr-1 text-gray-500" />
            <span className="text-gray-600">LCP:</span>
            <span className={`ml-1 font-mono ${getScoreColor(metrics.lcp, { good: 2500, poor: 4000 })}`}>
              {formatTime(metrics.lcp)}
            </span>
          </div>

          <div className="flex items-center">
            <span className="text-gray-600">FID:</span>
            <span className={`ml-1 font-mono ${getScoreColor(metrics.fid, { good: 100, poor: 300 })}`}>
              {formatTime(metrics.fid)}
            </span>
          </div>

          <div className="flex items-center">
            <span className="text-gray-600">CLS:</span>
            <span className={`ml-1 font-mono ${getScoreColor(metrics.cls * 1000, { good: 100, poor: 250 })}`}>
              {metrics.cls.toFixed(3)}
            </span>
          </div>
        </div>

        {/* Other Metrics */}
        <div className="border-t border-gray-100 pt-2 space-y-1">
          <div className="flex justify-between">
            <span className="text-gray-600">TTFB:</span>
            <span className="font-mono text-gray-900">{formatTime(metrics.ttfb)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">DOM:</span>
            <span className="font-mono text-gray-900">{formatTime(metrics.domContentLoaded)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Load:</span>
            <span className="font-mono text-gray-900">{formatTime(metrics.loadComplete)}</span>
          </div>
        </div>

        {/* Network Info */}
        {networkInfo && (
          <div className="border-t border-gray-100 pt-2">
            <div className="flex items-center mb-1">
              <Globe className="h-3 w-3 mr-1 text-gray-500" />
              <span className="text-gray-600">Network:</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">Type:</span>
              <span className="font-mono text-gray-900">{networkInfo.effectiveType}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">Speed:</span>
              <span className="font-mono text-gray-900">{networkInfo.downlink} Mbps</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">RTT:</span>
              <span className="font-mono text-gray-900">{networkInfo.rtt}ms</span>
            </div>
          </div>
        )}
      </div>

      <div className="mt-3 pt-2 border-t border-gray-100">
        <div className="text-xs text-gray-500 text-center">
          Dev Mode Only
        </div>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
