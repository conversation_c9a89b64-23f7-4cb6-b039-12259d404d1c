import React from 'react';

const PreloadResources: React.FC = () => {
  return (
    <>
      {/* Preload critical fonts */}
      <link
        rel="preload"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        as="style"
        onLoad={(e) => {
          const target = e.target as HTMLLinkElement;
          target.onload = null;
          target.rel = 'stylesheet';
        }}
      />
      
      {/* Preload logo */}
      <link rel="preload" href="/logo.png" as="image" />
      
      {/* DNS prefetch for external resources */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//apis.google.com" />
      <link rel="dns-prefetch" href="//www.googleapis.com" />
      <link rel="dns-prefetch" href="//firestore.googleapis.com" />
      <link rel="dns-prefetch" href="//shinesolution-a372c.firebaseapp.com" />

      {/* Preconnect to critical third-party origins */}
      <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      <link rel="preconnect" href="https://apis.google.com" crossOrigin="" />
      <link rel="preconnect" href="https://www.googleapis.com" crossOrigin="" />
      <link rel="preconnect" href="https://firestore.googleapis.com" crossOrigin="" />
      <link rel="preconnect" href="https://shinesolution-a372c.firebaseapp.com" crossOrigin="" />
    </>
  );
};

export default PreloadResources;
