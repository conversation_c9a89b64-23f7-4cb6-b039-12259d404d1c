// Core Web Vitals monitoring for production
// Based on Google's web-vitals library patterns

// Declare gtag function for Google Analytics
declare global {
  function gtag(...args: any[]): void;
}

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

interface WebVitalsConfig {
  reportToAnalytics?: boolean;
  reportToConsole?: boolean;
  reportToEndpoint?: string;
}

// Thresholds based on Google's Core Web Vitals
const THRESHOLDS = {
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  FID: { good: 100, poor: 300 },
  CLS: { good: 0.1, poor: 0.25 },
  TTFB: { good: 800, poor: 1800 },
  INP: { good: 200, poor: 500 }
};

function getRating(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

function reportMetric(metric: WebVitalMetric, config: WebVitalsConfig) {
  // Report to console in development or if explicitly enabled
  if (config.reportToConsole || !window.location.hostname.includes('persian.social')) {
    console.log(`${metric.name}: ${metric.value.toFixed(2)}ms (${metric.rating})`);
  }

  // Report to analytics (Google Analytics 4)
  if (config.reportToAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
    try {
      gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.value),
        custom_map: {
          metric_rating: metric.rating,
          metric_delta: metric.delta
        }
      });
    } catch (e) {
      console.warn('Failed to report to Google Analytics:', e);
    }
  }

  // Report to custom endpoint
  if (config.reportToEndpoint) {
    fetch(config.reportToEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        url: window.location.href,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        connection: (navigator as any).connection?.effectiveType || 'unknown'
      })
    }).catch(err => console.warn('Failed to report metric:', err));
  }
}

export function initWebVitals(config: WebVitalsConfig = {}) {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  const defaultConfig: WebVitalsConfig = {
    reportToAnalytics: true,
    reportToConsole: false,
    ...config
  };

  // First Contentful Paint (FCP)
  try {
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          const metric: WebVitalMetric = {
            name: 'FCP',
            value: entry.startTime,
            rating: getRating(entry.startTime, THRESHOLDS.FCP),
            delta: entry.startTime,
            id: 'fcp-' + Math.random().toString(36).substr(2, 9),
            navigationType: (performance.getEntriesByType('navigation')[0] as any)?.type || 'navigate'
          };
          reportMetric(metric, defaultConfig);
        }
      }
    }).observe({ entryTypes: ['paint'] });
  } catch (e) {
    console.warn('FCP monitoring not supported');
  }

  // Largest Contentful Paint (LCP)
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      const metric: WebVitalMetric = {
        name: 'LCP',
        value: lastEntry.startTime,
        rating: getRating(lastEntry.startTime, THRESHOLDS.LCP),
        delta: lastEntry.startTime,
        id: 'lcp-' + Math.random().toString(36).substr(2, 9),
        navigationType: (performance.getEntriesByType('navigation')[0] as any)?.type || 'navigate'
      };
      reportMetric(metric, defaultConfig);
    }).observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (e) {
    console.warn('LCP monitoring not supported');
  }

  // First Input Delay (FID)
  try {
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        const fidEntry = entry as any;
        const metric: WebVitalMetric = {
          name: 'FID',
          value: fidEntry.processingStart - fidEntry.startTime,
          rating: getRating(fidEntry.processingStart - fidEntry.startTime, THRESHOLDS.FID),
          delta: fidEntry.processingStart - fidEntry.startTime,
          id: 'fid-' + Math.random().toString(36).substr(2, 9),
          navigationType: (performance.getEntriesByType('navigation')[0] as any)?.type || 'navigate'
        };
        reportMetric(metric, defaultConfig);
      }
    }).observe({ entryTypes: ['first-input'] });
  } catch (e) {
    console.warn('FID monitoring not supported');
  }

  // Cumulative Layout Shift (CLS)
  try {
    let clsValue = 0;
    let clsEntries: any[] = [];

    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        const clsEntry = entry as any;
        if (!clsEntry.hadRecentInput) {
          clsValue += clsEntry.value;
          clsEntries.push(clsEntry);
        }
      }

      const metric: WebVitalMetric = {
        name: 'CLS',
        value: clsValue,
        rating: getRating(clsValue, THRESHOLDS.CLS),
        delta: clsValue,
        id: 'cls-' + Math.random().toString(36).substr(2, 9),
        navigationType: (performance.getEntriesByType('navigation')[0] as any)?.type || 'navigate'
      };
      reportMetric(metric, defaultConfig);
    }).observe({ entryTypes: ['layout-shift'] });
  } catch (e) {
    console.warn('CLS monitoring not supported');
  }

  // Time to First Byte (TTFB)
  try {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      const ttfb = navigation.responseStart - navigation.requestStart;
      const metric: WebVitalMetric = {
        name: 'TTFB',
        value: ttfb,
        rating: getRating(ttfb, THRESHOLDS.TTFB),
        delta: ttfb,
        id: 'ttfb-' + Math.random().toString(36).substr(2, 9),
        navigationType: navigation.type || 'navigate'
      };
      reportMetric(metric, defaultConfig);
    }
  } catch (e) {
    console.warn('TTFB monitoring not supported');
  }
}

// Initialize Web Vitals monitoring
export function startWebVitalsMonitoring() {
  // Wait for page load to ensure accurate measurements
  if (document.readyState === 'complete') {
    initWebVitals();
  } else {
    window.addEventListener('load', () => {
      setTimeout(() => initWebVitals(), 1000);
    });
  }
}

// Export for manual initialization
export { initWebVitals as default };
