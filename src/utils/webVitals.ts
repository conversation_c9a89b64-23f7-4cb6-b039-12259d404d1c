// Core Web Vitals monitoring for production
// Simplified version to avoid build issues

// Declare gtag function for Google Analytics
declare global {
  function gtag(...args: any[]): void;
}

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

interface WebVitalsConfig {
  reportToAnalytics?: boolean;
  reportToConsole?: boolean;
}

// Thresholds based on Google's Core Web Vitals
const THRESHOLDS = {
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  FID: { good: 100, poor: 300 },
  CLS: { good: 0.1, poor: 0.25 },
  TTFB: { good: 800, poor: 1800 }
};

function getRating(value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

function reportMetric(metric: WebVitalMetric, config: WebVitalsConfig) {
  // Report to console in development or if explicitly enabled
  if (config.reportToConsole) {
    console.log(`${metric.name}: ${metric.value.toFixed(2)}ms (${metric.rating})`);
  }

  // Report to analytics (Google Analytics 4)
  if (config.reportToAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
    try {
      gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.value)
      });
    } catch (e) {
      console.warn('Failed to report to Google Analytics:', e);
    }
  }
}

export function initWebVitals(config: WebVitalsConfig = {}) {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }

  const defaultConfig: WebVitalsConfig = {
    reportToAnalytics: true,
    reportToConsole: false,
    ...config
  };

  // First Contentful Paint (FCP)
  try {
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          const metric: WebVitalMetric = {
            name: 'FCP',
            value: entry.startTime,
            rating: getRating(entry.startTime, THRESHOLDS.FCP)
          };
          reportMetric(metric, defaultConfig);
        }
      }
    }).observe({ entryTypes: ['paint'] });
  } catch (e) {
    console.warn('FCP monitoring not supported');
  }

  // Largest Contentful Paint (LCP)
  try {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      const metric: WebVitalMetric = {
        name: 'LCP',
        value: lastEntry.startTime,
        rating: getRating(lastEntry.startTime, THRESHOLDS.LCP)
      };
      reportMetric(metric, defaultConfig);
    }).observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (e) {
    console.warn('LCP monitoring not supported');
  }

  // Time to First Byte (TTFB)
  try {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      const ttfb = navigation.responseStart - navigation.requestStart;
      const metric: WebVitalMetric = {
        name: 'TTFB',
        value: ttfb,
        rating: getRating(ttfb, THRESHOLDS.TTFB)
      };
      reportMetric(metric, defaultConfig);
    }
  } catch (e) {
    console.warn('TTFB monitoring not supported');
  }
}

// Initialize Web Vitals monitoring
export function startWebVitalsMonitoring() {
  if (typeof window !== 'undefined') {
    if (document.readyState === 'complete') {
      initWebVitals();
    } else {
      window.addEventListener('load', () => {
        setTimeout(() => initWebVitals(), 1000);
      });
    }
  }
}

// Export for manual initialization
export default initWebVitals;
