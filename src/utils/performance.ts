// Performance monitoring utilities
export const performanceUtils = {
  // Track component loading times
  trackComponentLoad: (componentName: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      const mark = `${componentName}-start`;
      performance.mark(mark);
      
      return () => {
        const endMark = `${componentName}-end`;
        performance.mark(endMark);
        performance.measure(componentName, mark, endMark);
        
        // Log performance data in development
        if (import.meta.env.DEV) {
          const entries = performance.getEntriesByName(componentName);
          const lastEntry = entries[entries.length - 1];
          console.log(`Component ${componentName} loaded in ${lastEntry.duration.toFixed(2)}ms`);
        }
      };
    }
    return () => {};
  },

  // Track lazy loading effectiveness
  trackLazyLoad: (chunkName: string) => {
    if (import.meta.env.DEV) {
      console.log(`Lazy loading chunk: ${chunkName}`);
    }
  },

  // Measure Core Web Vitals
  measureCoreWebVitals: () => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (import.meta.env.DEV) {
          console.log('LCP:', `${lastEntry.startTime.toFixed(2)}ms`);
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const fidEntry = entry as any; // PerformanceEventTiming
          if (import.meta.env.DEV) {
            console.log('FID:', `${(fidEntry.processingStart - fidEntry.startTime).toFixed(2)}ms`);
          }
        }
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const clsEntry = entry as any; // LayoutShift
          if (!clsEntry.hadRecentInput) {
            clsValue += clsEntry.value;
          }
        }
        if (import.meta.env.DEV) {
          console.log('CLS:', clsValue.toFixed(4));
        }
      }).observe({ entryTypes: ['layout-shift'] });
    }
  },

  // Bundle size tracking
  trackBundleSize: () => {
    if (typeof window !== 'undefined' && import.meta.env.DEV) {
      setTimeout(() => {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        const jsResources = resources.filter(resource =>
          resource.name.includes('.js') && resource.name.includes(window.location.origin)
        );

        let totalSize = 0;
        console.group('📦 Bundle Analysis');
        jsResources.forEach(resource => {
          const size = resource.transferSize || resource.encodedBodySize || 0;
          totalSize += size;
          console.log(`${resource.name.split('/').pop()}: ${(size / 1024).toFixed(2)} KB`);
        });
        console.log(`Total JS Bundle Size: ${(totalSize / 1024).toFixed(2)} KB`);
        console.groupEnd();
      }, 2000);
    }
  }
};
