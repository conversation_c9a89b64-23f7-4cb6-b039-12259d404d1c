// Performance monitoring utilities
export const performanceUtils = {
  // Track component loading times
  trackComponentLoad: function(componentName: string) {
    if (typeof window !== 'undefined' && window.performance) {
      const mark = componentName + '-start';
      performance.mark(mark);

      return function() {
        const endMark = componentName + '-end';
        performance.mark(endMark);
        performance.measure(componentName, mark, endMark);

        const entries = performance.getEntriesByName(componentName);
        const lastEntry = entries[entries.length - 1];
        console.log('Component ' + componentName + ' loaded in ' + lastEntry.duration.toFixed(2) + 'ms');
      };
    }
    return function() {};
  },

  // Track lazy loading effectiveness
  trackLazyLoad: function(chunkName: string) {
    console.log('Lazy loading chunk: ' + chunkName);
  },

  // Measure Core Web Vitals
  measureCoreWebVitals: function() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      try {
        new PerformanceObserver(function(entryList) {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log('LCP: ' + lastEntry.startTime.toFixed(2) + 'ms');
        }).observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.log('LCP monitoring not supported');
      }

      // First Input Delay (FID)
      try {
        new PerformanceObserver(function(entryList) {
          for (const entry of entryList.getEntries()) {
            const fidEntry = entry as any;
            console.log('FID: ' + (fidEntry.processingStart - fidEntry.startTime).toFixed(2) + 'ms');
          }
        }).observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.log('FID monitoring not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        new PerformanceObserver(function(entryList) {
          for (const entry of entryList.getEntries()) {
            const clsEntry = entry as any;
            if (!clsEntry.hadRecentInput) {
              clsValue += clsEntry.value;
            }
          }
          console.log('CLS: ' + clsValue.toFixed(4));
        }).observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.log('CLS monitoring not supported');
      }
    }
  },

  // Bundle size tracking
  trackBundleSize: function() {
    if (typeof window !== 'undefined') {
      setTimeout(function() {
        try {
          const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
          const jsResources = resources.filter(function(resource) {
            return resource.name.includes('.js') && resource.name.includes(window.location.origin);
          });

          let totalSize = 0;
          console.group('📦 Bundle Analysis');
          jsResources.forEach(function(resource) {
            const size = resource.transferSize || resource.encodedBodySize || 0;
            totalSize += size;
            const fileName = resource.name.split('/').pop();
            console.log(fileName + ': ' + (size / 1024).toFixed(2) + ' KB');
          });
          console.log('Total JS Bundle Size: ' + (totalSize / 1024).toFixed(2) + ' KB');
          console.groupEnd();
        } catch (e) {
          console.log('Bundle size tracking not supported');
        }
      }, 2000);
    }
  }
};
