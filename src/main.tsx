import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { performanceUtils } from './utils/performance'
import { registerSW } from './utils/serviceWorker'

// Initialize performance monitoring
performanceUtils.measureCoreWebVitals();
performanceUtils.trackBundleSize();

// Register service worker for caching
registerSW();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
