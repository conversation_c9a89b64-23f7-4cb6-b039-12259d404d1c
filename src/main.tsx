import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { performanceUtils } from './utils/performance'
import { registerSW } from './utils/serviceWorker'

// Initialize performance monitoring
if (process.env.NODE_ENV === 'development') {
  performanceUtils.measureCoreWebVitals();
  performanceUtils.trackBundleSize();
}

// Register service worker for caching
if (process.env.NODE_ENV === 'production') {
  registerSW();
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
